import { Home, Settings, Brain, Rocket, User, X, Search, Check, Menu, ChevronLeft, ChevronDown, ChevronRight } from "lucide-react";
import { Link, useLocation } from "wouter";
import { Button } from "@/ui/button";
import { Input } from "@/ui/input";
import { useAuth } from "@/hooks/use-auth";
import { useCompany } from "@/contexts/company-context";
import { useCompanyData } from "@/hooks/use-company-data";
import { CompanyService, CompanyInfo } from "@/services/company-service";
import { useState, useEffect } from "react";

interface SidebarProps {
  isJoyceOpen?: boolean;
}

export function Sidebar({ isJoyceOpen = false }: SidebarProps = {}) {
  const [location] = useLocation();
  const { user, logout } = useAuth();
  const { selectedCompany, setSelectedCompany, companyDisplayName } = useCompany();
  const { data: productsServicesData } = useCompanyData(selectedCompany, "products-services");
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<CompanyInfo[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [selectedCompanyFullName, setSelectedCompanyFullName] = useState<string>('');
  const [isCollapsed, setIsCollapsed] = useState(() => {
    // Load collapsed state from localStorage
    const saved = localStorage.getItem('sidebar-collapsed');
    return saved ? JSON.parse(saved) : false;
  });
  const [intelligenceExpanded, setIntelligenceExpanded] = useState(() => {
    // Auto-expand Intelligence if we're on an SEI report page
    return location.startsWith("/sei-report");
  });
  const [productsExpanded, setProductsExpanded] = useState(false);
  const companyService = CompanyService.getInstance();

  // Save collapsed state to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('sidebar-collapsed', JSON.stringify(isCollapsed));
  }, [isCollapsed]);

  // Auto-expand Intelligence section when on SEI report pages
  useEffect(() => {
    if (location.startsWith("/sei-report")) {
      setIntelligenceExpanded(true);
    }
  }, [location]);

  // Auto-collapse left sidebar when Joyce opens
  useEffect(() => {
    if (isJoyceOpen) {
      setIsCollapsed(true);
    }
  }, [isJoyceOpen]);

  useEffect(() => {
    const searchCompanies = async () => {
      if (showSearch) {
        const results = await companyService.searchCompanies(searchQuery);
        setSearchResults(results);
      }
    };
    searchCompanies();
  }, [searchQuery, showSearch]);


  useEffect(() => {
    // Fetch full company name when selected company changes
    const fetchCompanyFullName = async () => {
      if (selectedCompany) {
        try {
          const fullName = await companyService.getCompanyFullName(selectedCompany);
          setSelectedCompanyFullName(fullName);
        } catch (error) {
          console.error('Error fetching company full name:', error);
          setSelectedCompanyFullName(selectedCompany.toUpperCase());
        }
      }
    };

    fetchCompanyFullName();
  }, [selectedCompany, companyService]);

  const handleCompanySelect = (companyCode: string) => {
    setSelectedCompany(companyCode);
    setShowSearch(false);
    setSearchQuery('');
  };

  const handleClearCompany = () => {
    setShowSearch(true);
    setSearchQuery('');
  };

  const handleToggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  const handleNavClick = () => {
    setIsCollapsed(true);
  };

  const navigation = [
    { name: "Home", href: "/", icon: Home },
    { name: "Digital Mirror", href: "/digital-mirror", icon: Home },
    { name: "Readiness", href: "/execution-readiness", icon: Settings },
    { name: "Action", href: "/action", icon: Rocket },
  ];

  const seiReportTabs = [
    { id: "executive-summary", label: "Executive Summary" },
    { id: "enterprise-layer", label: "Enterprise Layer" },
    { id: "products-services", label: "Products & Services" },
    { id: "customer-engagement", label: "Customer Engagement" },
    { id: "organizational-maturity", label: "Organizational Maturity" },
    { id: "execution-roadmap", label: "Execution Roadmap" },
  ];

  return (
    <div className="w-64 bg-white border-r border-gray-200 flex flex-col h-screen">
      {/* Logo and Company Selector - Always visible */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center mb-4">
          <img 
            src="/images/rejoyce-logo--black.png" 
            alt="Rejoyce" 
            className="h-8 w-auto"
          />
        </div>
        
        <div className="space-y-2">
          <div className="text-sm font-medium text-gray-700">Focus Company</div>
          
          {!showSearch ? (
            <div className="flex items-center justify-between bg-gray-100 rounded px-3 py-2">
              <div className="flex-1">
                <div className="font-medium">{companyDisplayName}</div>
                <div className="text-xs text-gray-500">{selectedCompanyFullName}</div>
              </div>
              <X className="w-3 h-3 text-gray-400 cursor-pointer" onClick={handleClearCompany} />
            </div>
          ) : (
            <div className="space-y-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search companies..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 text-sm"
                  autoFocus
                />
              </div>
              
              {searchResults.length > 0 && (
                <div className="max-h-40 overflow-y-auto bg-white border border-gray-200 rounded-md shadow-sm">
                  {searchResults.map((company) => (
                    <div
                      key={company.code}
                      className="px-3 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                      onClick={() => handleCompanySelect(company.code)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-sm">{company.displayName}</div>
                          <div className="text-xs text-gray-500">{company.fullName}</div>
                        </div>
                        {selectedCompany === company.code && (
                          <Check className="w-4 h-4 text-green-600" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
              
              {searchResults.length === 0 && searchQuery && (
                <div className="text-sm text-gray-500 px-3 py-2 bg-gray-50 rounded">
                  No companies found matching "{searchQuery}"
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Collapsible Navigation Section */}
      <div className={`${isCollapsed ? 'w-16' : 'w-64'} transition-all duration-300 flex flex-col flex-1 relative`}>
        {/* Toggle Button - Positioned at consistent height */}
        <div className={`absolute ${isCollapsed ? 'top-5 left-1/2 transform -translate-x-1/2' : 'top-5 right-2'} z-10`}>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={handleToggleCollapse}
            className="h-8 w-8 p-0"
          >
            {isCollapsed ? (
              <Menu className="w-4 h-4" />
            ) : (
              <ChevronLeft className="w-4 h-4" />
            )}
          </Button>
        </div>
        
        {/* Navigation Menu */}
        <nav className={`flex-1 ${isCollapsed ? 'p-2 pt-12' : 'p-4 pt-12'}`}>
          <ul className="space-y-2">
            {/* Regular Navigation Items */}
            {navigation.map((item) => {
              const Icon = item.icon;
              const isActive = item.href === "/" 
                ? location === "/" 
                : location.startsWith(item.href);
              
              return (
                <li key={item.name}>
                  <Link href={item.href}>
                    <a 
                      className={`flex items-center ${isCollapsed ? 'justify-center p-2' : 'space-x-3 px-3 py-2'} rounded-lg ${
                        isActive
                          ? "bg-rejoyce-green text-white" 
                          : "text-gray-700 hover:bg-gray-100"
                      }`}
                      onClick={handleNavClick}
                      title={isCollapsed ? item.name : undefined}
                    >
                      <Icon className="w-5 h-5" />
                      {!isCollapsed && <span>{item.name}</span>}
                    </a>
                  </Link>
                </li>
              );
            })}

            {/* Intelligence (SEI Report) - Expandable */}
            <li>
              {isCollapsed ? (
                <Link href="/sei-report">
                  <a 
                    className={`flex items-center justify-center p-2 rounded-lg ${
                      location.startsWith("/sei-report")
                        ? "bg-rejoyce-green text-white" 
                        : "text-gray-700 hover:bg-gray-100"
                    }`}
                    onClick={handleNavClick}
                    title="Intelligence"
                  >
                    <Brain className="w-5 h-5" />
                  </a>
                </Link>
              ) : (
                <>
                  <div 
                    className="flex items-center justify-between cursor-pointer"
                    onClick={() => setIntelligenceExpanded(!intelligenceExpanded)}
                  >
                    <div className="flex items-center space-x-3 text-gray-700 px-3 py-2">
                      <Brain className="w-5 h-5" />
                      <span>Intelligence</span>
                    </div>
                    {intelligenceExpanded ? 
                      <ChevronDown className="w-4 h-4 text-gray-400" /> : 
                      <ChevronRight className="w-4 h-4 text-gray-400" />
                    }
                  </div>
                  {intelligenceExpanded && (
                    <ul className="ml-8 mt-1 space-y-1">
                      {seiReportTabs.map((tab) => (
                        <li key={tab.id}>
                          {tab.id === "products-services" ? (
                            <>
                              <div 
                                className="flex items-center justify-between cursor-pointer"
                                onClick={() => setProductsExpanded(!productsExpanded)}
                              >
                                <Link href={`/sei-report/${tab.id}`}>
                                  <a className={`flex-1 rounded-lg px-3 py-2 text-sm ${
                                    location === `/sei-report/${tab.id}` 
                                      ? "bg-rejoyce-green text-white" 
                                      : "text-gray-600 hover:bg-gray-100"
                                  }`}>
                                    {tab.label}
                                  </a>
                                </Link>
                                {productsExpanded ? 
                                  <ChevronDown className="w-3 h-3 text-gray-400 mr-2" /> : 
                                  <ChevronRight className="w-3 h-3 text-gray-400 mr-2" />
                                }
                              </div>
                              {productsExpanded && productsServicesData?.journeyStages && (
                                <ul className="ml-6 mt-1 space-y-1">
                                  {productsServicesData.journeyStages.map((stage: any) => (
                                    <li key={stage.id}>
                                      <button
                                        className="w-full text-left px-3 py-1 text-xs text-gray-500 hover:text-gray-700 hover:bg-gray-50 rounded"
                                        onClick={() => {
                                          // Handle journey stage navigation
                                          // This would need to be coordinated with the main page
                                        }}
                                      >
                                        {stage.label}
                                      </button>
                                    </li>
                                  ))}
                                </ul>
                              )}
                            </>
                          ) : (
                            <Link href={`/sei-report/${tab.id}`}>
                              <a className={`flex items-center space-x-2 rounded-lg px-3 py-2 text-sm ${
                                location === `/sei-report/${tab.id}` 
                                  ? "bg-rejoyce-green text-white" 
                                  : "text-gray-600 hover:bg-gray-100"
                              }`}>
                                <span>{tab.label}</span>
                              </a>
                            </Link>
                          )}
                        </li>
                      ))}
                    </ul>
                  )}
                </>
              )}
            </li>
          </ul>
        </nav>

        {/* User Section */}
        <div className={`${isCollapsed ? 'p-2' : 'p-4'} border-t border-gray-200`}>
          {isCollapsed ? (
            <div className="flex justify-center">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={logout}
                className="h-8 w-8 p-0"
                title="Logout"
              >
                <User className="w-4 h-4" />
              </Button>
            </div>
          ) : (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2 text-gray-700">
                <User className="w-4 h-4" />
                <span className="text-sm">{user?.username || "Analyst"}</span>
              </div>
              <Button variant="ghost" size="sm" onClick={logout}>
                Logout
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
