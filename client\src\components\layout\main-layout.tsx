import { useState, useEffect } from "react";
import { Sidebar } from "./sidebar";
import { JoyceSidebar } from "./joyce-sidebar";
import { ChatBubble } from "@/components/ui/chat-bubble";

interface MainLayoutProps {
  children: React.ReactNode;
}

export function MainLayout({ children }: MainLayoutProps) {
  const [isJoyceOpen, setIsJoyceOpen] = useState(false);

  const handleJoyceToggle = () => {
    setIsJoyceOpen(!isJoyceOpen);
  };

  const handleJoyceClose = () => {
    setIsJoyceOpen(false);
  };

  // Pass Joyce state to Sidebar to auto-collapse left sidebar when <PERSON> opens
  return (
    <div className="min-h-screen flex bg-gray-50 relative">
      {/* Left Sidebar */}
      <Sidebar isJoyceOpen={isJoyceOpen} />
      
      {/* Main Content Area */}
      <div 
        className={`flex-1 transition-all duration-300 ${
          isJoyceOpen ? 'mr-[400px]' : 'mr-0'
        }`}
      >
        {children}
      </div>

      {/* Chat Bubble */}
      <ChatBubble 
        onClick={handleJoyceToggle} 
        isActive={isJoyceOpen}
      />

      {/* Joyce Right Sidebar */}
      <JoyceSidebar 
        isOpen={isJoyceOpen} 
        onClose={handleJoyceClose} 
      />
    </div>
  );
}