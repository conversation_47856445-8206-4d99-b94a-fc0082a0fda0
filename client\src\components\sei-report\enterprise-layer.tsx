import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/ui/card";
import { Badge } from "@/ui/badge";
import { ChevronRight, ChevronLeft, TrendingUp, AlertTriangle, CheckCircle, BarChart3, Cloud, Lightbulb, Shield, Filter } from "lucide-react";
import { But<PERSON> } from "@/ui/button";
import { useEnterpriseLensData } from "@/hooks/use-lens-data";
import { useCompany } from "@/contexts/company-context";
import { getStatusClasses } from "@/lib/utils";
import { DataVisualization, generateChartConfig } from "@/components/ui/data-visualization";

interface EnterpriseLayerProps {
  onNextSection: () => void;
  onPrevSection: () => void;
  selectedLens?: string;
}

export function EnterpriseLayer({ onNextSection, onPrevSection, selectedLens = 'growth-expansion' }: EnterpriseLayerProps) {
  const { selectedCompany } = useCompany();
  const { data: contentData, loading: contentLoading, isLensApplied } = useEnterpriseLensData(selectedCompany, selectedLens);

  if (contentLoading) {
    return <div className="p-6">Loading...</div>;
  }

  if (!contentData) {
    return <div className="p-6">Failed to load content data</div>;
  }

  // Get the company field name dynamically from the first chart data item
  const getCompanyFieldName = () => {
    if (!contentData?.kpiBenchmarking?.chartData?.[0]) return 'verizon';
    const firstItem = contentData.kpiBenchmarking.chartData[0];
    const companyField = Object.keys(firstItem).find(key => key !== 'category' && key !== 'industry' && key !== 'color');
    return companyField || 'verizon';
  };

  const companyFieldName = getCompanyFieldName();

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'positive':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      case 'negative':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      default:
        return <TrendingUp className="w-4 h-4 text-blue-600" />;
    }
  };

  const getIconForInsight = (icon: string) => {
    switch (icon) {
      case 'chart':
        return <BarChart3 className="w-6 h-6" />;
      case 'gear':
        return <TrendingUp className="w-6 h-6" />;
      case 'lightbulb':
        return <Lightbulb className="w-6 h-6" />;
      default:
        return <BarChart3 className="w-6 h-6" />;
    }
  };

  return (
    <div className="p-6 space-y-8">
      {/* Enterprise Overview */}
      <section>
        <h2 className="text-xl font-bold text-gray-900 mb-4">{contentData.enterpriseOverview.title}</h2>
        <p className="text-gray-700 mb-6">
          {contentData.enterpriseOverview.description}
        </p>
      </section>

      {/* Lens Indicator */}
      {isLensApplied && contentData.lensContext && (
        <section>
          <Card className="border-l-4 border-l-blue-500 bg-blue-50">
            <CardContent className="pt-6">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <Filter className="w-4 h-4 text-white" />
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="text-sm font-medium text-blue-900 mb-1">
                    Strategic Lens: {contentData.lensContext.selectedLens.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </h3>
                  <p className="text-sm text-blue-700 mb-2">
                    Analysis Style: {contentData.lensContext.narrativeStyle}
                  </p>
                  <p className="text-sm text-blue-600">
                    Emphasizes: {contentData.lensContext.emphasis.slice(0, 3).join(', ')}
                  </p>
                  {contentData.lensSpecificInsights && contentData.lensSpecificInsights.length > 0 && (
                    <div className="mt-3 space-y-1">
                      {contentData.lensSpecificInsights.map((insight: string, index: number) => (
                        <div key={index} className="flex items-start space-x-2">
                          <ChevronRight className="w-3 h-3 mt-0.5 text-blue-600 flex-shrink-0" />
                          <span className="text-xs text-blue-700">{insight}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </section>
      )}

      {/* Executive Dashboard */}
      <section>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="w-5 h-5" />
              <span>Executive Dashboard</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-900">
                  {contentData.executiveDashboard.marketCapitalization.value}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {contentData.executiveDashboard.marketCapitalization.description}
                </div>
                <Badge variant={contentData.executiveDashboard.marketCapitalization.status === 'stable' ? 'secondary' : 'default'} className="mt-2">
                  {contentData.executiveDashboard.marketCapitalization.status}
                </Badge>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-900">
                  {contentData.executiveDashboard.revenue.value}
                </div>
                <div className="text-sm text-green-600 font-medium">
                  {contentData.executiveDashboard.revenue.change}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {contentData.executiveDashboard.revenue.description}
                </div>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-900">
                  {contentData.executiveDashboard.ebitdaMargin.value}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {contentData.executiveDashboard.ebitdaMargin.description}
                </div>
                <Badge variant="default" className="mt-2 bg-green-100 text-green-800">
                  {contentData.executiveDashboard.ebitdaMargin.status}
                </Badge>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-900">
                  {contentData.executiveDashboard.netDebtEbitda.value}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {contentData.executiveDashboard.netDebtEbitda.description}
                </div>
                <Badge variant="secondary" className="mt-2">
                  {contentData.executiveDashboard.netDebtEbitda.status}
                </Badge>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-900">
                  {contentData.executiveDashboard.esgRiskScore.value}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {contentData.executiveDashboard.esgRiskScore.description}
                </div>
                <Badge variant="default" className="mt-2 bg-green-100 text-green-800">
                  {contentData.executiveDashboard.esgRiskScore.status}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Core KPI Performance Dashboard */}
      <section>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="w-5 h-5" />
              <span>Core KPI Performance Dashboard</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 text-sm font-medium text-gray-700">Key Performance Indicator</th>
                    <th className="text-center py-3 text-sm font-medium text-gray-700">Actual</th>
                    <th className="text-center py-3 text-sm font-medium text-gray-700">Target</th>
                    <th className="text-center py-3 text-sm font-medium text-gray-700">Peer</th>
                    <th className="text-center py-3 text-sm font-medium text-gray-700">Status</th>
                    <th className="text-left py-3 text-sm font-medium text-gray-700">Commentary</th>
                  </tr>
                </thead>
                <tbody>
                  {contentData.coreKpiPerformance.metrics.map((metric, index) => (
                    <tr key={index} className={`border-b border-gray-100 ${metric.isEmphasized ? 'bg-blue-50' : ''}`}>
                      <td className="py-4 text-sm font-medium text-gray-900">
                        <div className="flex items-center space-x-2">
                          <span>{metric.indicator}</span>
                          {metric.isEmphasized && (
                            <Badge variant="secondary" className="bg-blue-100 text-blue-800 text-xs px-2 py-0.5">
                              Lens Focus
                            </Badge>
                          )}
                        </div>
                      </td>
                      <td className="py-4 text-sm text-center font-semibold">{metric.actual}</td>
                      <td className="py-4 text-sm text-center text-gray-600">{metric.target}</td>
                      <td className="py-4 text-sm text-center text-gray-600">{metric.peer}</td>
                      <td className="py-4 flex justify-center">
                        {getStatusIcon(metric.status)}
                      </td>
                      <td className="py-4 text-sm text-gray-600">{metric.commentary}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Fog Score & Data Confidence */}
      <section>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Cloud className="w-5 h-5" />
              <span>Fog Score & Data Confidence</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Fog Score Section */}
              <div className="flex flex-col sm:flex-row items-center sm:items-start space-y-4 sm:space-y-0 sm:space-x-4 p-4 bg-gray-50 rounded-lg">
                <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-yellow-100 text-yellow-800 text-2xl font-bold">
                  {contentData.fogScore.overall.value}
                </div>
                <div className="flex-1 text-center sm:text-left">
                  <div className="text-sm text-gray-600">/{contentData.fogScore.overall.maxValue}</div>
                  <div className="font-medium text-gray-900">{contentData.fogScore.overall.description}</div>
                  <p className="text-sm text-gray-600 mt-2">{contentData.fogScore.description}</p>
                </div>
              </div>
              
              {/* Data Confidence Breakdown */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-800 mb-2">{contentData.dataConfidence.directData.percentage}</div>
                  <div className="font-medium text-green-800 mb-2">Direct Data</div>
                  <div className="text-sm text-green-600">{contentData.dataConfidence.directData.description}</div>
                </div>
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-800 mb-2">{contentData.dataConfidence.proxyData.percentage}</div>
                  <div className="font-medium text-blue-800 mb-2">Proxy Data</div>
                  <div className="text-sm text-blue-600">{contentData.dataConfidence.proxyData.description}</div>
                </div>
                <div className="text-center p-4 bg-yellow-50 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-800 mb-2">{contentData.dataConfidence.inferredData.percentage}</div>
                  <div className="font-medium text-yellow-800 mb-2">Inferred Data</div>
                  <div className="text-sm text-yellow-600">{contentData.dataConfidence.inferredData.description}</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Charts Section */}
      <section>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Core KPI Benchmarking Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base font-medium text-gray-900">
                {contentData.kpiBenchmarking.title}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <DataVisualization
                type="bar"
                data={contentData.kpiBenchmarking.chartData}
                config={generateChartConfig(
                  contentData.kpiBenchmarking.chartData,
                  [companyFieldName, "industry"],
                  { 
                    [companyFieldName]: contentData.companyName,
                    industry: "Industry"
                  },
                  { 
                    [companyFieldName]: "#dc2626",
                    industry: "#0f766e"
                  }
                )}
                xKey="category"
                yKey={[companyFieldName, "industry"]}
                height={300}
                tooltip={{ enabled: true }}
                legend={{ enabled: true, position: "bottom" }}
                axes={{
                  xAxis: {
                    tickFormatter: (value: string) => {
                      // Shorten long labels to prevent overlap
                      if (value.length > 12) {
                        return value.substring(0, 10) + '...';
                      }
                      return value;
                    }
                  },
                  yAxis: {
                    domain: [0, 100]
                  }
                }}
              />
            </CardContent>
          </Card>

          {/* Performance Trends Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base font-medium text-gray-900">
                {contentData.performanceTrends.title}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <DataVisualization
                type="line"
                data={contentData.performanceTrends.chartData}
                config={generateChartConfig(
                  contentData.performanceTrends.chartData,
                  ["revenue", "margin"],
                  { 
                    revenue: "Revenue",
                    margin: "Margin"
                  },
                  { 
                    revenue: "#dc2626",
                    margin: "#ea580c"
                  }
                )}
                xKey="period"
                yKey={["revenue", "margin"]}
                height={300}
                tooltip={{ enabled: true }}
                legend={{ enabled: true, position: "bottom" }}
                axes={{
                  yAxis: {
                    tickFormatter: (value: any) => typeof value === 'number' ? value.toFixed(0) : value
                  }
                }}
              />
            </CardContent>
          </Card>
        </div>
      </section>

      {/* ESG & Risk Assessment */}
      <section>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="w-5 h-5" />
              <span>ESG & Risk Assessment</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 text-sm font-medium text-gray-700">Category</th>
                    <th className="text-left py-3 text-sm font-medium text-gray-700">Metric</th>
                    <th className="text-center py-3 text-sm font-medium text-gray-700">Value</th>
                    <th className="text-center py-3 text-sm font-medium text-gray-700">Status</th>
                    <th className="text-left py-3 text-sm font-medium text-gray-700">Note</th>
                  </tr>
                </thead>
                <tbody>
                  {contentData.esgRiskAssessment.categories.map((item, index) => (
                    <tr key={index} className="border-b border-gray-100">
                      <td className="py-4 text-sm font-medium text-gray-900">{item.category}</td>
                      <td className="py-4 text-sm text-gray-700">{item.metric}</td>
                      <td className="py-4 text-sm text-center font-semibold">{item.value}</td>
                      <td className="py-4 flex justify-center">
                        {getStatusIcon(item.status)}
                      </td>
                      <td className="py-4 text-sm text-gray-600">{item.note}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Strategic Metrics */}
      <section>
        <div className="grid grid-cols-1 gap-6">
          <h3 className="text-lg font-semibold text-gray-900">Strategic Metrics</h3>
          
          {/* Key Insights */}
          <div className="space-y-4">
            <h4 className="text-md font-medium text-gray-700">Key Insights</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {contentData.strategicMetrics.keyInsights.map((insight, index) => (
                <Card key={index} className="border-l-4" style={{borderLeftColor: insight.color === 'blue' ? '#3b82f6' : insight.color === 'green' ? '#10b981' : '#f59e0b'}}>
                  <CardContent className="p-4">
                    <div className="flex items-start space-x-3">
                      <div className={`p-2 rounded-lg ${insight.color === 'blue' ? 'bg-blue-100 text-blue-600' : insight.color === 'green' ? 'bg-green-100 text-green-600' : 'bg-yellow-100 text-yellow-600'}`}>
                        {getIconForInsight(insight.icon)}
                      </div>
                      <div className="flex-1">
                        <h5 className="font-medium text-gray-900 mb-2">{insight.title}</h5>
                        <p className="text-sm text-gray-600 mb-3">{insight.description}</p>
                        <ul className="text-xs text-gray-500 space-y-1">
                          {insight.highlights.map((highlight, idx) => (
                            <li key={idx}>• {highlight}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Opportunities */}
          <div className="space-y-4">
            <h4 className="text-md font-medium text-gray-700">Opportunities</h4>
            <div className="grid grid-cols-1 gap-4">
              {contentData.strategicMetrics.opportunities.map((opp, index) => (
                <Card key={index}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h5 className="font-medium text-gray-900">{opp.area}</h5>
                        <p className="text-sm text-gray-600 mt-1">{opp.description}</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={opp.priority === 'high' ? 'default' : 'secondary'}>
                          {opp.priority}
                        </Badge>
                        {getStatusIcon(opp.status)}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Navigation */}
      <div className="flex justify-between items-center pt-8 border-t border-gray-200">
        <Button variant="ghost" onClick={onPrevSection} className="flex items-center space-x-2">
          <ChevronLeft className="w-4 h-4" />
          <span>Previous Section</span>
        </Button>
        <Button onClick={onNextSection} className="flex items-center space-x-2">
          <span>Next Section</span>
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}
