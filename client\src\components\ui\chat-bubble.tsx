import { MessageCircle } from "lucide-react";
import { But<PERSON> } from "@/ui/button";

interface ChatBubbleProps {
  onClick: () => void;
  isActive?: boolean;
}

export function ChatBubble({ onClick, isActive = false }: ChatBubbleProps) {
  return (
    <div className="fixed right-6 z-50" style={{ top: 'calc(160px + 20px)' }}>
      <Button
        onClick={onClick}
        className={`h-12 w-12 rounded-full shadow-lg transition-all duration-300 hover:scale-110 ${
          isActive 
            ? "bg-rejoyce-green hover:bg-green-600 text-white" 
            : "bg-blue-600 hover:bg-blue-700 text-white"
        }`}
        size="sm"
      >
        <MessageCircle className="w-6 h-6" />
      </Button>
    </div>
  );
}