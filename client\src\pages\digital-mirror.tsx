import { MainLayout } from "@/components/layout/main-layout";
import { ComingSoon } from "@/components/ui/coming-soon";
import { Home } from "lucide-react";

export default function DigitalMirror() {
  return (
    <MainLayout>
      <ComingSoon
        title="Digital Mirror"
        description="Opens the investor-grade KPI view across Enterprise, Product/Service, and Customer layers with Joy Score, Fog Score, and proxy benchmarks. Preloaded with available data for selected company."
        icon={<Home className="w-10 h-10" />}
        features={[
          "Real-time KPI monitoring across all business layers",
          "Joy Score and Fog Score analytics with trend analysis",
          "Competitive benchmarking with proxy company comparisons",
          "Interactive dashboards with drill-down capabilities",
          "Automated alerts for performance threshold breaches",
          "Integration with external data sources for enriched insights"
        ]}
        estimatedDate="Q1 2025"
        showBackButton={false}
      />
    </MainLayout>
  );
}