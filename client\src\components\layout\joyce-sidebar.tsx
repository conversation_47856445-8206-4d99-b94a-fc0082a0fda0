import { useState } from "react";
import { <PERSON><PERSON> } from "@/ui/button";
import { Input } from "@/ui/input";
import { X, Send, Bot } from "lucide-react";

interface JoyceSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

export function JoyceSidebar({ isOpen, onClose }: JoyceSidebarProps) {
  const [message, setMessage] = useState("");
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: "system" as const,
      content: "Hello! I'm <PERSON>, your AI assistant. I can help you analyze the data you're viewing and answer questions about your strategic insights. What would you like to know?"
    }
  ]);

  const handleSendMessage = () => {
    if (!message.trim()) return;

    const newMessage = {
      id: messages.length + 1,
      type: "user" as const,
      content: message
    };

    setMessages(prev => [...prev, newMessage]);
    setMessage("");

    // Simulate AI response (replace with actual API call)
    setTimeout(() => {
      const aiResponse = {
        id: messages.length + 2,
        type: "ai" as const,
        content: "I understand your question about the current data. Let me analyze the information you're viewing and provide insights based on the strategic context."
      };
      setMessages(prev => [...prev, aiResponse]);
    }, 1000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div
      className={`fixed top-0 right-0 h-full bg-white border-l border-gray-200 shadow-xl transition-transform duration-300 z-40 ${
        isOpen ? 'translate-x-0' : 'translate-x-full'
      }`}
      style={{ width: '400px' }}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="flex items-center space-x-2">
          <Bot className="w-6 h-6" />
          <div>
            <h3 className="font-semibold">Joyce AI</h3>
            <p className="text-xs opacity-90">Strategic Intelligence Assistant</p>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="text-white hover:bg-white/10"
        >
          <X className="w-4 h-4" />
        </Button>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4" style={{ height: 'calc(100vh - 140px)' }}>
        {messages.map((msg) => (
          <div
            key={msg.id}
            className={`flex ${msg.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] rounded-lg px-3 py-2 ${
                msg.type === 'user'
                  ? 'bg-blue-600 text-white'
                  : msg.type === 'ai'
                  ? 'bg-gray-100 text-gray-900'
                  : 'bg-blue-50 text-blue-800 border border-blue-200'
              }`}
            >
              {msg.type === 'ai' && (
                <div className="flex items-center space-x-1 mb-1">
                  <Bot className="w-3 h-3" />
                  <span className="text-xs font-medium">Joyce</span>
                </div>
              )}
              <p className="text-sm">{msg.content}</p>
            </div>
          </div>
        ))}
      </div>

      {/* Input */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex space-x-2">
          <Input
            placeholder="Ask Joyce about this page..."
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            className="flex-1"
          />
          <Button onClick={handleSendMessage} size="sm">
            <Send className="w-4 h-4" />
          </Button>
        </div>
        <p className="text-xs text-gray-500 mt-2">
          Ask about the data you're viewing, strategic insights, or get recommendations.
        </p>
      </div>
    </div>
  );
}