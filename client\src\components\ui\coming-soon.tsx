import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>it<PERSON> } from "@/ui/card";
import { Badge } from "@/ui/badge";
import { Clock, ArrowLeft } from "lucide-react";
import { But<PERSON> } from "@/ui/button";
import { <PERSON> } from "wouter";

interface ComingSoonProps {
  title: string;
  description: string;
  features?: string[];
  icon?: React.ReactNode;
  estimatedDate?: string;
  showBackButton?: boolean;
}

export function ComingSoon({ 
  title, 
  description, 
  features = [], 
  icon,
  estimatedDate,
  showBackButton = true 
}: ComingSoonProps) {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-6">
      <div className="w-full max-w-2xl">
        {showBackButton && (
          <div className="mb-6">
            <Link href="/">
              <Button variant="ghost" className="flex items-center space-x-2">
                <ArrowLeft className="w-4 h-4" />
                <span>Back to Home</span>
              </Button>
            </Link>
          </div>
        )}
        
        <Card className="text-center">
          <CardHeader className="pb-4">
            <div className="flex justify-center mb-4">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-3xl">
                {icon || <Clock className="w-10 h-10" />}
              </div>
            </div>
            <CardTitle className="text-3xl font-bold text-gray-900 mb-2">
              {title}
            </CardTitle>
            <Badge variant="secondary" className="bg-blue-100 text-blue-800 text-sm px-3 py-1">
              Coming Soon
            </Badge>
          </CardHeader>
          
          <CardContent className="space-y-6">
            <p className="text-lg text-gray-600 leading-relaxed">
              {description}
            </p>
            
            {features.length > 0 && (
              <div className="text-left">
                <h3 className="text-lg font-semibold text-gray-800 mb-3">
                  What to expect:
                </h3>
                <ul className="space-y-2">
                  {features.map((feature, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
            
            {estimatedDate && (
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-center space-x-2 text-gray-600">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm">Estimated launch: {estimatedDate}</span>
                </div>
              </div>
            )}
            
            <div className="pt-4">
              <p className="text-sm text-gray-500">
                We're working hard to bring you this feature. Stay tuned for updates!
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}