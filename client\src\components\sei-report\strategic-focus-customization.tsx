import { useState } from "react";
import { <PERSON><PERSON> } from "@/ui/button";
import { Card } from "@/ui/card";
import { Badge } from "@/ui/badge";
import { X, ChevronRight } from "lucide-react";

interface StrategicLens {
  id: string;
  name: string;
  tooltip: string;
  purpose: string;
  focus: string[];
  bullets: string[];
}

const strategicLenses: StrategicLens[] = [
  {
    id: "growth-expansion",
    name: "Growth & Expansion",
    tooltip: "Focus analysis on scaling revenue through new markets, products, or customer segments. See how execution maturity and resource allocation align with growth opportunities.",
    purpose: "Highlights growth drivers, market expansion opportunities, and ROI of growth-focused initiatives.",
    focus: ["Enterprise Layer", "Product/Service Layer", "Customer Layer"],
    bullets: [
      "Revenue CAGR, market share, investability score",
      "Product revenue growth, profitability by segment", 
      "Acquisition, expansion CLTV, churn reduction"
    ]
  },
  {
    id: "cost-optimization",
    name: "Cost Optimization", 
    tooltip: "Focus analysis on reducing operating costs and improving efficiency. Identify execution gaps and quick wins to free up resources and increase profitability.",
    purpose: "Shifts analysis to cost structure, productivity drivers, and process maturity.",
    focus: ["Enterprise Layer", "Product/Service Layer", "Customer Layer"],
    bullets: [
      "EBITDA margin, SG&A efficiency, cost per unit",
      "Cost-to-serve by product/service, resource allocation",
      "CAC efficiency, retention cost"
    ]
  },
  {
    id: "innovation-transformation", 
    name: "Innovation & Transformation",
    tooltip: "Prioritize insights that drive new product development, digital transformation, or business model innovation.",
    purpose: "Elevates innovation metrics, R&D spend efficiency, and organizational agility scores.",
    focus: ["Enterprise Layer", "Product/Service Layer", "Customer Layer"],
    bullets: [
      "R&D ROI, agility scores, digital transformation metrics",
      "Pipeline innovation ROI, time-to-market",
      "Adoption rates of new offerings, innovation-driven CLTV"
    ]
  },
  {
    id: "operational-excellence",
    name: "Operational Excellence",
    tooltip: "Emphasize performance improvement, process optimization, and quality enhancements across enterprise operations.",
    purpose: "Prioritizes Lean flow, process maturity, and KPI consistency across functions.",
    focus: ["Enterprise Layer", "Product/Service Layer", "Customer Layer"],
    bullets: [
      "Process efficiency, KPI variance, execution maturity",
      "Lean flow metrics, capacity utilization", 
      "Service quality metrics, operational impact on CX"
    ]
  },
  {
    id: "customer-centricity",
    name: "Customer-Centricity", 
    tooltip: "Refocus analysis on customer acquisition, retention, and lifetime value. Identify execution gaps impacting the customer journey.",
    purpose: "Highlights customer metrics (CLTV, churn, NPS) and insights across the customer layer.",
    focus: ["Enterprise Layer", "Product/Service Layer", "Customer Layer"],
    bullets: [
      "NPS impact on revenue, churn-driven revenue leakage",
      "Cost-to-serve vs. customer profitability",
      "Customer satisfaction, loyalty, CLTV"
    ]
  },
  {
    id: "wildcard-ai",
    name: "Wildcard (AI-Driven)",
    tooltip: "Not sure where to start? Let Joyce analyze your data and suggest the most impactful strategic priority for your organization.",
    purpose: "Uses Joyce + the Mirror to recommend a tailored strategy lens based on current performance gaps and opportunity areas.",
    focus: ["Enterprise Layer", "Product/Service Layer", "Customer Layer"],
    bullets: [
      "Joyce suggests lens based on data gaps & high-ROI opportunities",
      "Prioritization dynamically adjusted",
      "Adjusted to highlight biggest customer-linked risks/opportunities"
    ]
  }
];

interface StrategicFocusCustomizationProps {
  selectedLens: string;
  onLensChange: (lens: string) => void;
  onClose: () => void;
}

export function StrategicFocusCustomization({ 
  selectedLens, 
  onLensChange, 
  onClose 
}: StrategicFocusCustomizationProps) {
  const selectedLensData = strategicLenses.find(lens => lens.id === selectedLens);
  
  return (
    <div className="absolute top-full left-0 mt-2 w-[800px] bg-white rounded-lg shadow-lg border border-gray-200 z-50">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Strategic Lens Selection</h3>
            <p className="text-sm text-gray-600 mt-1">
              Select a strategic lens to customize how the report emphasizes different metrics and insights:
            </p>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Strategic Lens Options */}
        <div className="space-y-3">
          {strategicLenses.map((lens) => (
            <Card 
              key={lens.id}
              className={`p-4 cursor-pointer transition-all hover:shadow-md ${
                selectedLens === lens.id ? 'ring-2 ring-blue-500 bg-blue-50' : ''
              }`}
              onClick={() => onLensChange(lens.id)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h4 className="font-medium text-gray-900">{lens.name}</h4>
                    <div className="flex space-x-1">
                      {lens.focus.map((focus) => (
                        <Badge 
                          key={focus} 
                          variant="outline" 
                          className="text-xs px-2 py-0.5"
                        >
                          {focus}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{lens.tooltip}</p>
                  <p className="text-xs text-gray-500">
                    <strong>Primary Purpose:</strong> {lens.purpose}
                  </p>
                </div>
                <div className="ml-4">
                  <div className={`w-4 h-4 rounded-full border-2 ${
                    selectedLens === lens.id 
                      ? 'border-blue-500 bg-blue-500' 
                      : 'border-gray-300'
                  }`}>
                    {selectedLens === lens.id && (
                      <div className="w-2 h-2 bg-white rounded-full m-0.5"></div>
                    )}
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
          <Button onClick={onClose}>
            Close
          </Button>
        </div>
      </div>
    </div>
  );
}